package com.maguo.loan.cash.flow;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
/**
 * <AUTHOR> gale
 * @Classname LoanCashFlowApplication
 * @Date 2025/5/16 11:42
 */

@SpringBootApplication(exclude = {SecurityAutoConfiguration.class})
@EnableFeignClients(basePackages = "com.maguo.loan.cash.flow.remote")
public class LoanCashFlowApplication {

    public static void main(String[] args) {
        SpringApplication.run(LoanCashFlowApplication.class, args);
    }

}
