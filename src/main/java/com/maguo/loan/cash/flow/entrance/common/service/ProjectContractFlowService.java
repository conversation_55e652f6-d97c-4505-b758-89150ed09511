package com.maguo.loan.cash.flow.entrance.common.service;

import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import com.maguo.loan.cash.flow.entity.common.ProjectInfo;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.repository.ProjectContractFlowRepository;
import com.maguo.loan.cash.flow.repository.ProjectInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/8/18 16:49
 */
@Service
public class ProjectContractFlowService {

    @Autowired
    private ProjectContractFlowRepository projectContractFlowRepository;

    /**
     * 根据项目编码查询项目合同集合
     *
     * @param projectCode 项目编码
     * @return 项目信息
     */
    public List<ProjectContractFlow> getProjectFlows(String projectCode) {
        return projectContractFlowRepository.findProjectContractFlowsByProjectCodeAndEnabled(projectCode,AbleStatus.ENABLE);
    }

    /**
     * 根据签章codo查询项目的唯一合同
     * @param agreementCode 签章编码
     * @return 项目信息
     */
    public ProjectContractFlow getProjectContractFlowsByAgreementCode(String agreementCode) {
        return projectContractFlowRepository.findProjectContractFlowsByAgreementCodeAndEnabled(agreementCode, AbleStatus.ENABLE);
    }

    /**
     * 根据项目编码和放款阶段查询项目合同集合
     *
     * @param projectCode 项目编码
     * @param loanStage   放款阶段
     * @return 项目合同集合
     */
    public List<ProjectContractFlow> getProjectFlowsByLoanStage(String projectCode, LoanStage loanStage) {
        return projectContractFlowRepository.findProjectContractFlowsByProjectCodeAndLoanStageAndEnabled(projectCode, loanStage,AbleStatus.ENABLE);
    }
}
