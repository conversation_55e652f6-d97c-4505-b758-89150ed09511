package com.maguo.loan.cash.flow.controller;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.maguo.loan.cash.flow.remote.manage.ProjectInfoFeign;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后管系统产品信息相关接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/21 11:30
 */
@Slf4j
@RestController
@RequestMapping("/manage/projectInfo")
public class ManageProjectController {
    private static final Logger logger = LoggerFactory.getLogger(ManageProjectController.class);

    @Autowired
    private ProjectInfoFeign projectInfoFeign;

    /**
     * HXBK统一回调入口
     * 接收外部HXBK的所有类型回调请求，透传到Capital模块处理
     *
     * @return 回调响应
     */
    @GetMapping("/query/{projectCode}")
    public RestResult<ProjectInfoDto> handle(@PathVariable("projectCode") String projectCode) {
        ProjectInfoDto projectInfoDto = projectInfoFeign.queryProjectInfo(projectCode);
        return RestResult.success(projectInfoDto);
    }
}
