package com.jinghang.cash.modules.manage.mapstruct;

import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.capital.api.dto.loan.LoanQueryDto;
import com.jinghang.capital.api.dto.loan.LoanVoucherResultDto;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T10:18:02+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
public class VoucherFileMapstructImpl implements VoucherFileMapstruct {

    @Override
    public FileDownloadDto copy(FileDownloadDto fileDownloadDto) {
        if ( fileDownloadDto == null ) {
            return null;
        }

        FileDownloadDto fileDownloadDto1 = new FileDownloadDto();

        fileDownloadDto1.setLoanId( fileDownloadDto.getLoanId() );
        fileDownloadDto1.setLoanOrderId( fileDownloadDto.getLoanOrderId() );
        fileDownloadDto1.setType( fileDownloadDto.getType() );
        fileDownloadDto1.setFileName( fileDownloadDto.getFileName() );
        fileDownloadDto1.setProduct( fileDownloadDto.getProduct() );
        fileDownloadDto1.setBankChannel( fileDownloadDto.getBankChannel() );
        fileDownloadDto1.setFileDate( fileDownloadDto.getFileDate() );

        return fileDownloadDto1;
    }

    @Override
    public FileDownloadResultDto copy(FileDownloadResultDto fileDownloadDto) {
        if ( fileDownloadDto == null ) {
            return null;
        }

        FileDownloadResultDto fileDownloadResultDto = new FileDownloadResultDto();

        fileDownloadResultDto.setFileStatus( fileDownloadDto.getFileStatus() );
        fileDownloadResultDto.setOssBucket( fileDownloadDto.getOssBucket() );
        fileDownloadResultDto.setOssPath( fileDownloadDto.getOssPath() );
        fileDownloadResultDto.setFileUrl( fileDownloadDto.getFileUrl() );
        fileDownloadResultDto.setFileName( fileDownloadDto.getFileName() );
        fileDownloadResultDto.setSignStatus( fileDownloadDto.getSignStatus() );

        return fileDownloadResultDto;
    }

    @Override
    public LoanQueryDto copy(LoanQueryDto fileDownloadDto) {
        if ( fileDownloadDto == null ) {
            return null;
        }

        LoanQueryDto loanQueryDto = new LoanQueryDto();

        loanQueryDto.setPageNum( fileDownloadDto.getPageNum() );
        loanQueryDto.setPageSize( fileDownloadDto.getPageSize() );
        loanQueryDto.setCustMobile( fileDownloadDto.getCustMobile() );
        loanQueryDto.setCustCertNo( fileDownloadDto.getCustCertNo() );
        loanQueryDto.setLoanId( fileDownloadDto.getLoanId() );
        loanQueryDto.setSysId( fileDownloadDto.getSysId() );

        return loanQueryDto;
    }

    @Override
    public LoanVoucherResultDto copy(LoanVoucherResultDto fileDownloadDto) {
        if ( fileDownloadDto == null ) {
            return null;
        }

        LoanVoucherResultDto loanVoucherResultDto = new LoanVoucherResultDto();

        loanVoucherResultDto.setId( fileDownloadDto.getId() );
        loanVoucherResultDto.setCustName( fileDownloadDto.getCustName() );
        loanVoucherResultDto.setCustMobile( fileDownloadDto.getCustMobile() );
        loanVoucherResultDto.setCustCertNo( fileDownloadDto.getCustCertNo() );
        loanVoucherResultDto.setLoanAmt( fileDownloadDto.getLoanAmt() );
        loanVoucherResultDto.setPeriods( fileDownloadDto.getPeriods() );
        loanVoucherResultDto.setLoanTime( fileDownloadDto.getLoanTime() );
        loanVoucherResultDto.setChannel( fileDownloadDto.getChannel() );
        loanVoucherResultDto.setIsClear( fileDownloadDto.getIsClear() );
        loanVoucherResultDto.setClearTime( fileDownloadDto.getClearTime() );

        return loanVoucherResultDto;
    }
}
